# 智能商旅碳管家 - 前端开发设计文档

**版本**: 1.0
**日期**: 2025年5月24日
**作者**: Manus AI

## 1. 概述

本文档为智能商旅碳管家项目的前端开发设计指南，旨在为前端开发和设计团队提供统一的技术栈、UI框架、设计风格和组件库参考，确保产品界面现代、简约、美观且易用。

### 1.1 项目背景

智能商旅碳管家是一款面向企业的SaaS服务，帮助企业精准核算、分析和管理商旅活动产生的碳排放。产品需要呈现复杂的数据分析结果，同时保持界面简洁直观，为用户提供良好的使用体验。

### 1.2 设计目标

- **现代简约**: 采用现代简约设计风格，减少视觉噪音，突出核心数据和功能
- **数据可视化**: 高效直观地展示碳排放数据和分析结果
- **响应式设计**: 确保在不同设备上（桌面、平板、移动端）提供一致的用户体验
- **可访问性**: 符合WCAG 2.1 AA级标准，确保不同能力用户都能顺畅使用
- **性能优化**: 快速加载和响应，提供流畅的交互体验

## 2. 技术栈

### 2.1 核心框架

- **React 18+**: 采用最新的React框架，利用其高效的组件化开发模式和丰富的生态系统
- **TypeScript 5.0+**: 提供类型安全，增强代码质量和开发效率
- **Next.js 14+**: 用于服务端渲染(SSR)和静态站点生成(SSG)，提升首屏加载速度和SEO表现

### 2.2 状态管理

- **Redux Toolkit**: 简化Redux使用，内置最佳实践，用于全局状态管理
- **React Query 5+**: 用于服务器状态管理，处理数据获取、缓存和同步
- **Zustand**: 轻量级状态管理库，用于局部或中等复杂度的状态管理

### 2.3 UI框架

- **主框架**: shadcn/ui
  - 基于Radix UI和Tailwind CSS构建的现代组件集合
  - 非传统npm包，而是可复制到项目中的组件代码
  - 完全可定制，高度灵活，设计美观
  - 无样式锁定，可根据项目需求自由调整

- **辅助框架**: 
  - **Radix UI**: shadcn/ui的底层基础，提供无障碍、无样式的UI原语
  - **Headless UI**: 提供无样式但功能完整的UI组件，可作为补充

### 2.4 CSS解决方案

- **主方案**: Tailwind CSS 4.0+
  - 实用优先的CSS框架，提高开发效率
  - 与shadcn/ui完美集成，是其样式基础
  - 内置响应式设计支持

- **CSS-in-JS (可选)**: 
  - **Styled Components 6.0+**: 用于特殊场景的组件级样式定制
  - **Vanilla Extract**: 类型安全的CSS-in-JS解决方案

- **CSS变量**: 用于主题定制和动态样式变化

### 2.5 构建工具

- **Vite 5.0+**: 快速的前端构建工具，提供即时热更新
- **Turbopack**: Next.js内置的高性能打包工具
- **ESLint & Prettier**: 代码质量和格式化工具

### 2.6 测试框架

- **Vitest**: 快速的单元测试框架
- **React Testing Library**: 组件测试
- **Cypress**: 端到端测试
- **Storybook 8.0+**: 组件开发、文档和测试环境

## 3. 设计系统

### 3.1 设计风格

采用**现代简约科技风格**，具有以下特点：

- 简洁明了的界面布局
- 扁平化设计元素
- 适当的留白和呼吸空间
- 清晰的视觉层次
- 微妙的动效和过渡

### 3.2 配色方案

#### 3.2.1 主色调

采用**科技蓝**为主色调，搭配**环保绿**作为辅助色，体现科技感和环保主题：

- **主色**: `#0ea5e9` (科技蓝)
- **辅助色**: `#10b981` (环保绿)
- **中性色**: 
  - 背景: `#f8fafc` (浅灰)
  - 文本: `#1e293b` (深灰)
  - 边框: `#e2e8f0` (中灰)

#### 3.2.2 功能色

- **成功**: `#10b981` (绿色)
- **警告**: `#f59e0b` (黄色)
- **错误**: `#ef4444` (红色)
- **信息**: `#0ea5e9` (蓝色)

#### 3.2.3 数据可视化色板

为确保数据可视化的清晰度和可辨识性，提供10色数据可视化色板：

```
['#0ea5e9', '#10b981', '#8b5cf6', '#f59e0b', '#ec4899', 
 '#eab308', '#84cc16', '#06b6d4', '#f97316', '#ef4444']
```

这些颜色经过精心选择，确保在各种图表中具有良好的区分度，同时保持视觉和谐。

### 3.3 排版

#### 3.3.1 字体系统

- **主要字体**: 
  - 中文: `'PingFang SC', 'Microsoft YaHei', sans-serif`
  - 英文: `'Inter', -apple-system, BlinkMacSystemFont, sans-serif`

- **代码字体**: `'JetBrains Mono', 'Fira Code', monospace`

#### 3.3.2 字体大小

采用基于4的比例系统：

- **超大标题**: 36px (2.25rem)
- **大标题**: 30px (1.875rem)
- **中标题**: 24px (1.5rem)
- **小标题**: 20px (1.25rem)
- **正文**: 16px (1rem)
- **辅助文本**: 14px (0.875rem)
- **小文本**: 12px (0.75rem)

#### 3.3.3 行高

- **标题**: 1.4
- **正文**: 1.6
- **紧凑文本**: 1.2

### 3.4 间距系统

采用4px为基础单位的间距系统：

- **超小**: 4px (0.25rem)
- **小**: 8px (0.5rem)
- **中小**: 12px (0.75rem)
- **中**: 16px (1rem)
- **中大**: 24px (1.5rem)
- **大**: 32px (2rem)
- **超大**: 48px (3rem)

### 3.5 阴影与深度

使用阴影创建UI元素的深度感：

```css
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
```

### 3.6 圆角

统一的圆角系统：

- **小**: 2px
- **中**: 4px
- **大**: 8px
- **圆形**: 50%

### 3.7 动效设计

#### 3.7.1 过渡动画

- **快速**: 150ms
- **标准**: 250ms
- **缓慢**: 400ms

#### 3.7.2 缓动函数

- **标准**: `cubic-bezier(0.4, 0, 0.2, 1)`
- **进入**: `cubic-bezier(0, 0, 0.2, 1)`
- **退出**: `cubic-bezier(0.4, 0, 1, 1)`

#### 3.7.3 动效原则

- 自然、流畅、有目的
- 提供视觉反馈
- 引导用户注意力
- 不干扰用户操作

## 4. 资源库

### 4.1 图标库

#### 4.1.1 主要图标库

- **[Lucide Icons](https://lucide.dev/)**
  - shadcn/ui默认集成的图标库
  - 超过1000个精美的开源图标
  - 简约现代风格，与shadcn/ui设计语言一致

- **[Tabler Icons](https://tabler-icons.io/)**
  - 3000+开源SVG图标
  - 简约现代风格
  - 支持多种尺寸和描边宽度

#### 4.1.2 辅助图标库

- **[Phosphor Icons](https://phosphoricons.com/)**
  - 灵活的开源图标集
  - 支持6种风格变体
  - 一致的设计语言

- **[Heroicons](https://heroicons.com/)**
  - Tailwind团队出品的图标库
  - 与Tailwind CSS和shadcn/ui风格协调
  - 提供实心和线性两种风格

### 4.2 图片资源

#### 4.2.1 免费高质量图片

- **[Unsplash](https://unsplash.com/)**
  - 高质量免费图片
  - 简单的API集成
  - 商业使用友好

- **[Pexels](https://www.pexels.com/)**
  - 多样化的免费图片和视频
  - 支持API集成
  - 无需归因

#### 4.2.2 插画资源

- **[unDraw](https://undraw.co/)**
  - 开源插画
  - 支持自定义主色调
  - SVG格式，易于集成和定制

- **[Storyset](https://storyset.com/)**
  - 可定制的插画集
  - 支持动画版本
  - 多种风格可选

#### 4.2.3 背景资源

- **[Hero Patterns](https://heropatterns.com/)**
  - SVG背景模式
  - 轻量级且可定制
  - 适合作为页面或卡片背景

- **[Cool Backgrounds](https://coolbackgrounds.io/)**
  - 生成独特的背景图案
  - 多种风格可选
  - 高分辨率导出

### 4.3 数据可视化组件

#### 4.3.1 图表库

- **[Recharts](https://recharts.org/)**
  - 基于React和D3的图表库
  - 声明式API，易于使用
  - 高度可定制，与shadcn/ui风格协调

- **[Tremor](https://www.tremor.so/)**
  - 基于React和Tailwind CSS的数据可视化库
  - 与shadcn/ui设计语言高度兼容
  - 提供丰富的图表和数据展示组件

- **[React Charts](https://react-charts.tanstack.com/)**
  - TanStack生态系统的图表库
  - 高性能，支持大数据集
  - 简洁API，易于集成

#### 4.3.2 地图可视化

- **[React Simple Maps](https://www.react-simple-maps.io/)**
  - 基于D3-geo的简单地图组件
  - 轻量级且易于使用
  - 支持自定义地图投影和交互

- **[Mapbox GL JS](https://docs.mapbox.com/mapbox-gl-js/guides/)**
  - 高性能的矢量地图库
  - 支持自定义样式和交互
  - 适合复杂的地理数据可视化

#### 4.3.3 数据网格

- **[TanStack Table](https://tanstack.com/table/v8)**
  - 高度可定制的表格解决方案
  - 无头UI设计，与shadcn/ui完美集成
  - 强大的数据处理能力

- **[shadcn/ui Table](https://ui.shadcn.com/docs/components/table)**
  - 基于Radix UI的表格组件
  - 与shadcn/ui设计系统一致
  - 支持基本的排序和分页功能

## 5. 组件设计

### 5.1 组件结构

采用原子设计方法论，将UI组件分为以下层次：

1. **原子 (Atoms)**: 基础UI元素，如按钮、输入框、图标等
2. **分子 (Molecules)**: 由多个原子组成的简单组件，如搜索框、表单项等
3. **有机体 (Organisms)**: 复杂的组件组合，如导航栏、数据表格、图表卡片等
4. **模板 (Templates)**: 页面布局框架
5. **页面 (Pages)**: 完整的用户界面

### 5.2 核心组件

#### 5.2.1 布局组件

- **PageLayout**: 页面整体布局，包含侧边栏、顶部导航和内容区
- **DashboardGrid**: 仪表盘网格布局，支持拖拽调整
- **ContentCard**: 内容卡片，用于包装各类数据展示组件

#### 5.2.2 数据展示组件

- **EmissionsSummaryCard**: 碳排放汇总卡片
- **EmissionsTrendChart**: 碳排放趋势图表
- **EmissionsBreakdownPie**: 碳排放类型分布饼图
- **DepartmentRankingTable**: 部门排名表格
- **UserEmissionsCard**: 用户碳排放卡片

#### 5.2.3 交互组件

- **DateRangePicker**: 日期范围选择器
- **FilterDropdown**: 筛选下拉菜单
- **ActionButton**: 操作按钮
- **NotificationBadge**: 通知徽章
- **ConfirmationModal**: 确认对话框

### 5.3 响应式设计

采用移动优先的响应式设计策略，定义以下断点：

- **xs**: < 576px (手机)
- **sm**: ≥ 576px (平板竖屏)
- **md**: ≥ 768px (平板横屏)
- **lg**: ≥ 992px (笔记本)
- **xl**: ≥ 1200px (桌面)
- **xxl**: ≥ 1600px (大屏)

每个组件需要适配这些断点，确保在不同设备上提供最佳体验。

### 5.4 可访问性指南

- 所有交互元素必须支持键盘操作
- 使用语义化HTML元素
- 提供适当的颜色对比度
- 添加ARIA属性增强屏幕阅读器体验
- 确保表单元素有关联的标签
- 提供错误信息的多种反馈方式

## 6. 开发最佳实践

### 6.1 组件开发规范

- 采用函数式组件和React Hooks
- 使用TypeScript定义明确的props和状态类型
- 遵循单一职责原则，每个组件只做一件事
- 使用React.memo()优化渲染性能
- 将复杂逻辑抽离到自定义Hooks中

```tsx
// 组件示例
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowUpIcon, ArrowDownIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { EmissionData } from '@/types';

interface EmissionsSummaryCardProps {
  data: EmissionData;
  period: 'daily' | 'weekly' | 'monthly';
  loading?: boolean;
  onPeriodChange?: (period: string) => void;
}

export const EmissionsSummaryCard: React.FC<EmissionsSummaryCardProps> = ({
  data,
  period,
  loading = false,
  onPeriodChange,
}) => {
  const { total, previousPeriod } = data;
  const isIncrease = total > previousPeriod.value;
  const changePercentage = Math.abs(
    ((total - previousPeriod.value) / previousPeriod.value) * 100
  ).toFixed(1);

  return (
    <Card className={loading ? "opacity-60 pointer-events-none" : ""}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">碳排放总量</CardTitle>
        <Select
          value={period}
          onValueChange={onPeriodChange}
          disabled={loading}
        >
          <SelectTrigger className="w-[80px] h-8">
            <SelectValue placeholder="周期" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="daily">日</SelectItem>
            <SelectItem value="weekly">周</SelectItem>
            <SelectItem value="monthly">月</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{total.toFixed(2)} kg CO2e</div>
        <div className="flex items-center mt-2">
          <Badge 
            variant={isIncrease ? "destructive" : "success"}
            className="mr-2"
          >
            {isIncrease ? (
              <ArrowUpIcon className="h-3 w-3 mr-1" />
            ) : (
              <ArrowDownIcon className="h-3 w-3 mr-1" />
            )}
            {changePercentage}%
          </Badge>
          <span className="text-sm text-muted-foreground">
            相比{previousPeriod.label}
          </span>
        </div>
      </CardContent>
    </Card>
  );
};
```

### 6.2 状态管理最佳实践

- 使用React Query处理服务器状态
- 使用Redux Toolkit管理全局UI状态
- 使用Context API处理主题等应用级配置
- 使用Zustand管理局部复杂状态

```tsx
// React Query示例
import { useQuery } from '@tanstack/react-query';
import { fetchEmissionsSummary } from '@/api/emissions';

export const useEmissionsSummary = (params) => {
  return useQuery({
    queryKey: ['emissions', 'summary', params],
    queryFn: () => fetchEmissionsSummary(params),
    staleTime: 5 * 60 * 1000, // 5分钟
  });
};

// 组件中使用
const EmissionsDashboard = () => {
  const [dateRange, setDateRange] = useState([startDate, endDate]);
  const { data, isLoading, error } = useEmissionsSummary({
    startDate: dateRange[0],
    endDate: dateRange[1],
  });
  
  // 渲染逻辑
};
```

### 6.3 性能优化策略

- 使用React.memo()避免不必要的重渲染
- 实现虚拟滚动处理大量数据
- 使用React.lazy()和Suspense实现代码分割
- 优化图片和资源加载
- 使用Web Workers处理复杂计算

```tsx
// 代码分割示例
import React, { lazy, Suspense } from 'react';

const EmissionsReport = lazy(() => import('@/components/EmissionsReport'));

const ReportPage = () => {
  return (
    <div>
      <h1>碳排放报告</h1>
      <Suspense fallback={<div>加载中...</div>}>
        <EmissionsReport />
      </Suspense>
    </div>
  );
};
```

### 6.4 样式管理

- 使用Tailwind CSS的@apply指令创建可复用样式
- 使用CSS变量实现主题切换
- 组件级样式使用CSS Modules或Styled Components
- 避免内联样式，除非是动态计算的值

```tsx
// shadcn/ui组件样式定制示例
// components/ui/button.tsx
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        // 自定义变体
        eco: "bg-green-600 text-white hover:bg-green-700",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
```

## 7. 页面设计

### 7.1 关键页面

#### 7.1.1 登录页

简洁现代的登录界面，包含：
- 品牌标识和产品名称
- 登录表单（用户名/密码）
- 记住我选项
- 忘记密码链接
- 企业代码输入（多租户）

#### 7.1.2 仪表盘页面

企业碳排放总览仪表盘，包含：
- 顶部筛选器（时间范围、部门等）
- 关键指标卡片（总排放量、同比变化等）
- 排放趋势图表
- 排放类型分布图
- 部门排名表格
- 待处理异常告警

#### 7.1.3 详情分析页面

深入分析页面，包含：
- 多维度数据筛选器
- 详细的数据表格
- 多种图表展示
- 数据导出功能

#### 7.1.4 用户个人碳足迹页面

员工个人碳排放页面，包含：
- 个人排放总量和趋势
- 与部门/公司平均水平对比
- 历史行程列表
- 个性化低碳建议

### 7.2 导航结构

采用左侧固定导航栏 + 顶部次级导航的结构：

- **主导航**（左侧）
  - 仪表盘
  - 碳排放分析
  - 部门管理
  - 员工管理
  - 报表中心
  - 系统设置

- **次级导航**（顶部）
  - 根据主导航项目动态变化
  - 例如：碳排放分析下可包含"趋势分析"、"类型分析"、"异常检测"等

### 7.3 响应式布局策略

- 在小屏设备上将左侧导航转为可折叠抽屉菜单
- 卡片式组件在小屏幕上垂直堆叠
- 表格在小屏幕上简化列数或转为卡片视图
- 图表根据屏幕宽度动态调整大小和细节

## 8. 实现示例

### 8.1 项目结构

```
src/
├── api/                # API请求函数
├── assets/             # 静态资源
├── components/         # 共享组件
│   ├── ui/             # shadcn/ui基础组件
│   ├── atoms/          # 原子组件
│   ├── molecules/      # 分子组件
│   ├── organisms/      # 有机体组件
│   └── templates/      # 页面模板
├── config/             # 配置文件
├── constants/          # 常量定义
├── hooks/              # 自定义Hooks
├── layouts/            # 布局组件
├── lib/                # 工具库
├── pages/              # 页面组件
├── services/           # 服务层
├── store/              # 状态管理
├── styles/             # 全局样式
├── types/              # TypeScript类型定义
└── utils/              # 工具函数
```

### 8.2 组件示例

#### 仪表盘页面组件

```tsx
// pages/Dashboard.tsx
import React, { useState } from 'react';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { PageHeader } from '@/components/molecules';
import { 
  EmissionsSummaryCard,
  EmissionsTrendChart,
  EmissionsBreakdownPie,
  DepartmentRankingTable,
  AnomalyAlertsList
} from '@/components/organisms';
import { useEmissionsSummary, useDepartmentRanking } from '@/hooks';
import { DashboardLayout } from '@/layouts';

const Dashboard: React.FC = () => {
  const [dateRange, setDateRange] = useState({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    to: new Date()
  });
  const [period, setPeriod] = useState('monthly');
  
  const { 
    data: summaryData, 
    isLoading: isSummaryLoading 
  } = useEmissionsSummary({
    startDate: format(dateRange.from, 'yyyy-MM-dd'),
    endDate: format(dateRange.to, 'yyyy-MM-dd'),
    period
  });
  
  const {
    data: rankingData,
    isLoading: isRankingLoading
  } = useDepartmentRanking({
    date: format(new Date(), 'yyyy-MM'),
    period,
    limit: 10
  });
  
  const handleDateChange = (range) => {
    setDateRange(range);
  };
  
  const handlePeriodChange = (value) => {
    setPeriod(value);
  };
  
  return (
    <DashboardLayout>
      <PageHeader
        title="企业碳排放仪表盘"
        subtitle="全面了解您企业的碳排放情况"
        className="mb-6"
      >
        <div className="flex items-center gap-4">
          <DatePickerWithRange
            date={dateRange}
            onDateChange={handleDateChange}
          />
          <Select value={period} onValueChange={handlePeriodChange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="选择周期" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">日</SelectItem>
              <SelectItem value="weekly">周</SelectItem>
              <SelectItem value="monthly">月</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </PageHeader>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <EmissionsSummaryCard 
          data={summaryData?.total} 
          loading={isSummaryLoading}
        />
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">同比变化</CardTitle>
          </CardHeader>
          <CardContent>
            {/* 同比变化内容 */}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">人均排放</CardTitle>
          </CardHeader>
          <CardContent>
            {/* 人均排放内容 */}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">异常告警</CardTitle>
          </CardHeader>
          <CardContent>
            {/* 异常告警内容 */}
          </CardContent>
        </Card>
      </div>
      
      <div className="grid gap-6 mt-6 md:grid-cols-7">
        <div className="col-span-5">
          <EmissionsTrendChart 
            data={summaryData?.trend} 
            loading={isSummaryLoading}
          />
        </div>
        <div className="col-span-2">
          <EmissionsBreakdownPie 
            data={summaryData?.byType} 
            loading={isSummaryLoading}
          />
        </div>
      </div>
      
      <div className="grid gap-6 mt-6 md:grid-cols-7">
        <div className="col-span-5">
          <DepartmentRankingTable 
            data={rankingData} 
            loading={isRankingLoading}
          />
        </div>
        <div className="col-span-2">
          <AnomalyAlertsList />
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Dashboard;
```

### 8.3 主题配置示例

```tsx
// lib/themes.ts
export const lightTheme = {
  // 颜色
  colors: {
    primary: '#0ea5e9',
    secondary: '#10b981',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#0ea5e9',
    background: '#f8fafc',
    card: '#ffffff',
    text: {
      primary: '#1e293b',
      secondary: '#64748b',
      disabled: '#cbd5e1',
    },
    border: '#e2e8f0',
  },
  
  // 阴影
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  },
  
  // 圆角
  radii: {
    sm: '2px',
    md: '4px',
    lg: '8px',
    full: '9999px',
  },
  
  // 动画
  transitions: {
    fast: '150ms',
    normal: '250ms',
    slow: '400ms',
  },
};

export const darkTheme = {
  // 暗色主题配置
  colors: {
    primary: '#0ea5e9',
    secondary: '#10b981',
    // 其他颜色...
    background: '#0f172a',
    card: '#1e293b',
    text: {
      primary: '#f8fafc',
      secondary: '#cbd5e1',
      disabled: '#64748b',
    },
    border: '#334155',
  },
  // 其他配置保持不变...
};
```

## 9. shadcn/ui 安装与使用

### 9.1 安装步骤

1. **设置项目**:
   ```bash
   # 创建Next.js项目
   npx create-next-app@latest my-app --typescript --tailwind --eslint
   cd my-app
   ```

2. **安装shadcn/ui CLI**:
   ```bash
   npm install -D @shadcn/ui
   ```

3. **初始化shadcn/ui**:
   ```bash
   npx shadcn-ui init
   ```
   在初始化过程中，会询问一系列问题，如样式偏好、主题等。

4. **添加组件**:
   ```bash
   # 例如添加按钮组件
   npx shadcn-ui add button
   
   # 添加卡片组件
   npx shadcn-ui add card
   
   # 添加表格组件
   npx shadcn-ui add table
   ```

### 9.2 组件定制

shadcn/ui的一大特点是组件代码直接复制到项目中，可以完全自由定制：

1. **修改组件样式**:
   直接编辑`components/ui/`目录下的组件文件

2. **创建新的变体**:
   使用class-variance-authority (cva)添加新的变体

3. **主题定制**:
   编辑`app/globals.css`中的CSS变量

```css
/* 在globals.css中定制主题 */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 199 89% 48%; /* 科技蓝 */
  --primary-foreground: 210 40% 98%;
  --secondary: 160 84% 39%; /* 环保绿 */
  --secondary-foreground: 210 40% 98%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 199 89% 48%;
  --radius: 0.5rem;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 199 89% 48%;
  --primary-foreground: 210 40% 98%;
  --secondary: 160 84% 39%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 199 89% 48%;
}
```

### 9.3 与其他库集成

shadcn/ui可以与其他库无缝集成：

1. **与React Hook Form集成**:
   ```bash
   npx shadcn-ui add form
   ```

2. **与React Query集成**:
   shadcn/ui组件可以直接与React Query结合使用

3. **与数据可视化库集成**:
   可以将Recharts或Tremor的图表包装在shadcn/ui的Card组件中

## 10. 开发流程

### 10.1 组件开发流程

1. 在Storybook中设计和开发组件
2. 编写组件单元测试
3. 在页面中集成组件
4. 进行端到端测试

### 10.2 设计协作

1. 使用Figma进行UI设计和原型制作
2. 设计师提供组件设计规范和交互说明
3. 开发人员根据设计规范实现组件
4. 定期进行设计评审和调整

### 10.3 质量保证

1. 使用ESLint和Prettier确保代码质量和一致性
2. 编写单元测试和集成测试
3. 进行性能测试和可访问性测试
4. 进行跨浏览器和跨设备测试

## 11. 参考资源

### 11.1 设计系统参考

- [shadcn/ui 官方文档](https://ui.shadcn.com/)
- [Radix UI 文档](https://www.radix-ui.com/)
- [Material Design](https://material.io/design)
- [Atlassian Design System](https://atlassian.design/)

### 11.2 工具和库

- [React 官方文档](https://reactjs.org/docs/getting-started.html)
- [Next.js 文档](https://nextjs.org/docs)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [shadcn/ui 组件库](https://ui.shadcn.com/docs/components)
- [Recharts 数据可视化](https://recharts.org/)

### 11.3 学习资源

- [React Patterns](https://reactpatterns.com/)
- [Atomic Design by Brad Frost](https://atomicdesign.bradfrost.com/)
- [Refactoring UI](https://www.refactoringui.com/)
- [React Query 官方教程](https://tanstack.com/query/latest/docs/react/overview)

---

本文档旨在为智能商旅碳管家项目的前端开发和设计团队提供统一的技术和设计指南。随着项目的发展，本文档将持续更新和完善。
