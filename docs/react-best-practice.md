# React.js 最佳实践指南

本项目是基于vite+react.js 开发的前端项目，请基于以下核心原则进行开发，以便最佳开发实践：

## 项目结构

### 目录组织

```
src/
├── assets/          # 静态资源文件
├── components/      # 共享/通用组件
│   ├── ui/          # 基础UI组件
│   └── layout/      # 布局组件
├── features/        # 按功能模块组织的代码
│   └── [feature]/   # 特定功能模块
│       ├── components/  # 模块专用组件
│       ├── hooks/       # 模块专用钩子
│       ├── services/    # 模块相关API调用
│       └── utils/       # 模块工具函数
├── hooks/           # 全局共享钩子
├── pages/           # 页面级组件
├── services/        # API服务和数据获取
├── store/           # 全局状态管理
├── types/           # TypeScript类型定义
├── utils/           # 工具函数
└── App.tsx          # 应用入口组件
```

### 最佳实践

- 采用**特性优先**的目录结构，而非类型优先
- 将相关代码放在一起，减少跨目录导航
- 避免嵌套超过3层的目录结构
- 组件文件命名使用PascalCase (如`Button.tsx`)
- 非组件文件使用camelCase (如`useAuth.ts`)
- 每个文件只导出一个主要功能，便于自动导入

## 组件设计

### 组件划分

- 遵循**单一职责原则**，每个组件只专注于一个功能点
- 组件代码超过100行时考虑拆分
- 区分**展示组件**(UI呈现)和**容器组件**(数据和业务逻辑)
- 组件复杂度增加时，考虑使用Compound Components模式


### 组件实现

- 使用**函数组件**和React Hooks，避免类组件
- 使用TypeScript定义props类型，提高代码可靠性
- props数量超过3个时使用对象解构
- 设置合理的默认props
- 避免过度使用内联函数和对象


## 状态管理

### 局部状态

- 使用`useState`管理简单组件状态
- 使用`useReducer`管理复杂组件状态
- 避免状态提升过高，保持状态靠近使用它的组件

### 全局状态

- 小型应用使用Context API管理全局状态
- 分离UI状态和业务状态
- 遵循不可变数据模式更新状态


## 自定义钩子

- 创建自定义钩子封装重复逻辑
- 钩子命名以`use`开头，使用camelCase
- 钩子应该是纯函数，避免副作用
- 钩子应该有明确的返回值类型


## 性能优化

- 使用`React.memo`避免不必要的重渲染
- 使用`useMemo`缓存计算结果
- 使用`useCallback`缓存函数引用
- 实现虚拟列表渲染大数据集
- 使用React.lazy和Suspense实现代码分割


## API和数据获取

- 使用React Query或SWR管理服务器状态
- 将API调用逻辑与UI组件分离
- 实现数据缓存和重试机制
- 统一处理API错误


## 样式管理

- 使用CSS Modules或styled-components实现样式隔离
- 采用移动优先的响应式设计
- 使用CSS变量管理主题和颜色
- 避免内联样式，除非动态计算
- 组织样式，避免选择器嵌套过深


## 测试策略

- 编写单元测试验证组件行为
- 使用React Testing Library进行组件测试
- 使用Mock Service Worker模拟API请求
- 实现端到端测试验证关键用户流程


## 构建和优化

- 使用Vite的构建优化功能
- 配置适当的代码分割策略
- 优化依赖项，避免引入过大的库
- 使用现代化的打包工具和优化插件代码质量和规

## 使用ESLint和Prettier保持代码质量和一致性
- 实施代码审查流程
- 编写有意义的提交信息
- 遵循语义化版本控制