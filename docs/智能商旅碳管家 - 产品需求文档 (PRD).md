# 智能商旅碳管家 - 产品需求文档 (PRD)

**版本**: 1.0
**日期**: 2025年5月24日
**作者**: Manus AI

## 1. 引言

### 1.1 项目背景

随着全球对可持续发展和企业社会责任(CSR)的日益关注，以及ESG（环境、社会和公司治理）报告要求的普及，企业面临着越来越大的压力来管理和减少其运营活动中的碳排放。商务旅行是许多企业重要的碳排放源之一。同程商旅和携程商旅作为国内领先的差旅管理公司(TMC)，拥有庞大的企业客户基础和海量的商旅订单数据。利用这些现有资源，开发一款智能商旅碳管家产品，不仅能满足企业客户日益增长的碳管理需求，还能为平台创造新的价值增长点，提升市场竞争力。

### 1.2 产品目标

智能商旅碳管家旨在成为一款领先的企业商旅碳排放管理SaaS解决方案，帮助企业：

*   **精准核算**：基于权威标准和平台数据，自动、准确地计算商旅活动产生的碳排放。
*   **全面洞察**：提供多维度、可视化的碳排放数据分析报表，揭示排放来源、趋势和关键驱动因素。
*   **有效减排**：通过AI驱动的低碳推荐和目标管理，赋能企业和员工采取实际行动减少碳足迹。
*   **合规报告**：简化ESG报告流程，提供符合主流标准的碳排放数据和报告。
*   **提升管理**：通过异常检测和精细化管理，优化差旅政策，降低成本和碳排放。

### 1.3 产品范围

**本期范围 (MVP)**：

*   **数据对接**：与同程/携程商旅平台API对接，自动同步企业客户授权的机票、酒店、火车票、用车订单数据。
*   **碳排放计算**：基于国际认可标准（如GHG Protocol）和选定的第三方API（如ClimateTrade），计算各类商旅活动的碳排放。
*   **数据可视化与报表**：提供企业、部门、员工层级的碳排放仪表盘和基础报表（按时间、类型、部门等维度）。
*   **基础用户与组织管理**：支持企业管理员管理部门和员工信息。
*   **AI能力集成 (核心)**：
    *   碳排放异常检测（识别数据错误或异常模式）。
    *   基础的低碳推荐（如高铁替代短途航班）。
*   **SaaS基础**：多租户架构、基础订阅管理。

**远期范围 (后续迭代)**：

*   更精细化的碳排放计算模型（考虑具体机型、酒店能效等）。
*   更复杂的AI能力：个性化推荐、NLP洞察生成、碳排放预测、票据识别。
*   碳减排目标设定与追踪。
*   碳抵消市场对接。
*   与其他企业系统（如ERP、HR系统）的集成。
*   更丰富的报表和自定义报表功能。
*   移动端应用。

### 1.4 目标用户

*   **企业管理员/ESG负责人**：负责企业整体碳排放管理、报告和减排策略制定。
*   **部门经理**：关注部门碳排放情况，管理团队差旅行为。
*   **普通员工**：了解个人碳足迹，接收低碳出行建议。
*   **平台运营/客服人员**：协助企业客户使用系统，处理问题。

## 2. 用户角色与场景 (User Stories)

### 2.1 企业管理员 (Alice)

*   **用户画像**：某中型科技公司行政总监兼ESG负责人，关注公司整体运营效率和合规性，需要向管理层汇报ESG绩效。
*   **用户故事**：
    *   **注册与配置**：作为企业管理员，我希望能轻松注册并配置我们公司的账户，导入部门和员工信息，以便开始使用碳管家服务。
    *   **数据概览**：作为企业管理员，我希望在仪表盘上快速了解公司整体的商旅碳排放总量、趋势、主要来源以及与上期/同期的对比，以便掌握整体情况。
    *   **部门分析**：作为企业管理员，我希望能查看各部门的碳排放排名和详细数据，以便识别高排放部门并推动改进。
    *   **异常监控**：作为企业管理员，我希望能收到碳排放异常数据的告警，并能方便地查看和处理这些异常，以确保数据的准确性。
    *   **报表生成**：作为企业管理员，我希望能生成符合公司内部和外部（如ESG报告）需求的碳排放报表（月度/季度/年度），以便进行汇报和审计。
    *   **用户管理**：作为企业管理员，我希望能管理员工账户（增删改查、分配部门、设置角色），以便维护组织结构信息。
    *   **系统设置**：作为企业管理员，我希望能配置公司的基本信息和碳排放计算偏好（如是否包含RFI因子），以便系统按需运行。

### 2.2 部门经理 (Bob)

*   **用户画像**：销售部经理，关注团队业绩和成本控制，需要确保团队差旅符合公司政策，并响应公司的减排要求。
*   **用户故事**：
    *   **部门概览**：作为部门经理，我希望能查看我部门的碳排放总量、人均排放、趋势以及在公司内的排名，以便了解部门表现。
    *   **成员分析**：作为部门经理，我希望能查看部门内各成员的碳排放情况，以便进行针对性的沟通和管理。
    *   **行程审批辅助**：作为部门经理，我希望在审批员工差旅申请时（若与审批流程集成），能看到预估的碳排放信息，以便做出更环保的选择。
    *   **接收减排建议**：作为部门经理，我希望能收到针对我部门的减排建议和最佳实践，以便引导团队降低碳排放。

### 2.3 普通员工 (Charlie)

*   **用户画像**：市场部普通员工，经常需要出差拜访客户，关注个人差旅安排的便捷性和合规性，对环保有一定意识。
*   **用户故事**：
    *   **个人足迹查看**：作为普通员工，我希望能查看自己历史差旅的碳排放记录和统计数据，以便了解自己的碳足迹。
    *   **低碳选项感知**：作为普通员工，我希望在预订差旅（通过同程/携程商旅平台）时，能看到不同选项的碳排放对比和低碳标识，以便做出更环保的选择。
    *   **接收个性化建议**：作为普通员工，我希望能收到针对我个人出行习惯的低碳建议（如选择火车替代短途飞机），以便改进我的出行方式。
    *   **了解公司目标**：作为普通员工，我希望能了解公司和部门的减排目标及进展，以便更有参与感。

## 3. 功能需求 (Functional Requirements)

### 3.1 数据同步与管理模块

#### 3.1.1 同程/携程商旅数据同步

*   **FR-DS-001**: 系统需支持企业管理员配置同程/携程商旅平台的API凭证，建立安全连接。
*   **FR-DS-002**: 系统需根据配置的频率（如每日）自动从商旅平台API拉取授权企业员工的机票、酒店、火车票、用车订单数据。
*   **FR-DS-003**: 系统需支持手动触发特定时间范围的数据同步。
*   **FR-DS-004**: 同步的数据需存储在`travel_orders`表中，包含订单ID、类型、状态、时间、金额、用户ID、出发地、目的地、交通工具详情（航班号、车次、酒店名称等）等关键信息。（详见`database_design.md`）
*   **FR-DS-005**: 系统需记录每次同步任务的状态（开始时间、结束时间、处理记录数、成功数、失败数）并提供查询界面。
*   **FR-DS-006**: 对于同步失败的记录，需提供错误详情和重试机制。
*   **FR-DS-007**: 数据同步过程需考虑API调用频率限制和错误处理。

#### 3.1.2 手动数据导入

*   **FR-DS-008**: 系统需支持企业管理员通过CSV模板导入补充的商旅数据（如未通过平台预订的行程）。
*   **FR-DS-009**: 导入功能需提供数据校验，对格式错误或不完整的数据进行提示。

#### 3.1.3 数据清洗与标准化

*   **FR-DS-010**: 系统需对同步和导入的数据进行清洗和标准化，例如：统一机场/火车站代码、城市名称、酒店品牌等。
*   **FR-DS-011**: 对于缺失的关键信息（如酒店地址、用车距离），系统应尝试通过规则或第三方服务进行补充或估算，并标记数据来源和置信度。

### 3.2 碳排放计算引擎模块

#### 3.2.1 碳排放因子管理

*   **FR-CE-001**: 系统需内置一套基于国际标准（如GHG Protocol, DEFRA）和中国本土化数据的碳排放因子库，覆盖机票（按距离/航线、舱位）、酒店（按星级/类型、入住天数）、火车（按类型、距离）、用车（按车型、距离）等。
*   **FR-CE-002**: 排放因子库需支持版本管理和定期更新。
*   **FR-CE-003**: （可选）支持企业管理员根据自身情况自定义部分排放因子。

#### 3.2.2 碳排放计算逻辑

*   **FR-CE-004**: 系统需根据`travel_orders`表中的订单信息和排放因子库，自动计算每笔订单的碳排放量（CO2e）。
*   **FR-CE-005**: 计算逻辑需遵循`carbon_calculation_models.md`中定义的模型和算法。
    *   **机票**：优先使用API（如ClimateTrade）基于起降机场、舱位计算；若API不可用或未配置，则使用基于距离和舱位等级的排放因子计算，考虑是否包含RFI因子（根据企业设置）。
    *   **酒店**：基于入住天数、酒店星级/类型和所在地区的平均排放因子计算。
    *   **火车**：基于乘坐距离和火车类型（高铁、普通）的排放因子计算。
    *   **用车**：基于行驶距离和车辆类型（燃油、混动、电动）的排放因子计算；若距离未知，尝试根据起讫点估算。
*   **FR-CE-006**: 计算结果需存储在`order_carbon_emissions`表中，包含订单ID、排放量、单位、计算方法、计算时间、不确定性评估等。（详见`database_design.md`）
*   **FR-CE-007**: 系统需提供API端点（`/api/v1/carbon-emissions/calculate`）用于单次行程的碳排放估算。（详见`api_integration_specifications.md`）

#### 3.2.3 第三方API集成

*   **FR-CE-008**: 系统需支持集成第三方碳排放计算API（如ClimateTrade），企业管理员可选择配置并优先使用该API进行计算。
*   **FR-CE-009**: 系统需处理第三方API的调用失败或超时情况，回退到内置因子库计算。

### 3.3 仪表盘与报表模块

#### 3.3.1 企业碳排放仪表盘

*   **FR-DR-001**: 企业管理员登录后，默认展示企业碳排放仪表盘。
*   **FR-DR-002**: 仪表盘需展示关键指标（KPIs）：
    *   指定时间范围内的总碳排放量。
    *   与上期/同期的碳排放量对比（增减百分比）。
    *   按商旅类型（机票、酒店、火车、用车）的排放分布（饼图/柱状图）。
    *   碳排放趋势图（按日/周/月）。
    *   排放量最高的Top N部门排名。
    *   待处理的异常告警数量。
*   **FR-DR-003**: 仪表盘需支持时间范围选择（如本月、上月、本季度、本年、自定义）。
*   **FR-DR-004**: 仪表盘图表需具备交互性（如下钻、悬停提示）。

#### 3.3.2 部门碳排放仪表盘

*   **FR-DR-005**: 部门经理登录后，默认展示其所负责部门的碳排放仪表盘。
*   **FR-DR-006**: 仪表盘需展示部门关键指标：
    *   部门总碳排放量及占比。
    *   部门人均碳排放量。
    *   与上期/同期的对比。
    *   部门内按商旅类型的排放分布。
    *   部门碳排放趋势图。
    *   部门内员工排放量排名。
*   **FR-DR-007**: 支持时间范围选择。

#### 3.3.3 员工个人碳足迹

*   **FR-DR-008**: 普通员工登录后，可访问个人碳足迹页面。
*   **FR-DR-009**: 页面需展示个人关键指标：
    *   指定时间范围内的总碳排放量。
    *   与上期/同期的对比。
    *   与部门/公司平均水平的对比。
    *   个人按商旅类型的排放分布。
    *   个人碳排放趋势图。
    *   历史行程列表及其碳排放量。
*   **FR-DR-010**: 支持时间范围选择。

#### 3.3.4 碳排放报表

*   **FR-DR-011**: 系统需提供标准化的碳排放报表，包括：
    *   企业碳排放汇总报表。
    *   部门碳排放对比报表。
    *   员工碳排放明细报表。
*   **FR-DR-012**: 报表需支持按时间范围、部门、员工等维度进行筛选和聚合。
*   **FR-DR-013**: 报表需支持导出为CSV和PDF格式。（通过API `/api/v1/reports/carbon-emissions`）

### 3.4 AI能力模块

#### 3.4.1 碳排放异常检测

*   **FR-AI-001**: 系统需集成碳排放异常检测模型（如Isolation Forest），定期（如每日）自动扫描新增的订单碳排放数据。（详见`ai_integration_design.md`）
*   **FR-AI-002**: 检测模型需能识别单个订单排放量过高/过低、用户/部门排放模式突变等异常情况。
*   **FR-AI-003**: 检测到的异常需记录在`carbon_emission_anomalies`表中，包含异常类型、涉及实体、严重程度、时间等。
*   **FR-AI-004**: 企业管理员需能在界面上查看未处理的异常列表，并进行处理（标记为误报、创建处理任务等）。
*   **FR-AI-005**: 仪表盘需提示未处理的异常数量。

#### 3.4.2 低碳推荐引擎

*   **FR-AI-006**: 系统需集成低碳推荐引擎，为员工提供可行的低碳出行建议。（详见`ai_integration_design.md`）
*   **FR-AI-007**: MVP阶段，推荐引擎主要基于规则（如：识别短途（<800km）航班，推荐高铁替代方案）。
*   **FR-AI-008**: 推荐结果需展示在员工个人碳足迹页面或仪表盘上。
*   **FR-AI-009**: 推荐内容需包含建议描述、预计减排量或百分比。
*   **FR-AI-010**: 系统需记录用户对推荐的反馈（查看、采纳、忽略）。

#### 3.4.3 （远期）NLP洞察生成

*   **FR-AI-011**: （远期）系统需集成NLP能力，将仪表盘和报表中的关键数据转化为自然语言摘要和洞察。
*   **FR-AI-012**: （远期）洞察文本需展示在相关图表或报表摘要区域。

#### 3.4.4 （远期）碳排放预测

*   **FR-AI-013**: （远期）系统需集成时间序列预测模型，预测企业/部门未来的碳排放趋势。
*   **FR-AI-014**: （远期）预测结果需以图表形式展示在仪表盘或报表中。

### 3.5 用户与企业管理模块

#### 3.5.1 企业账户管理

*   **FR-UM-001**: 支持企业注册和信息管理（企业名称、行业、规模等）。
*   **FR-UM-002**: 支持企业订阅计划管理（查看当前计划、升级等）。

#### 3.5.2 组织架构管理

*   **FR-UM-003**: 企业管理员需能管理部门信息（创建、编辑、删除、设置层级关系）。
*   **FR-UM-004**: 支持通过CSV批量导入/更新部门信息。

#### 3.5.3 用户账户管理

*   **FR-UM-005**: 企业管理员需能管理员工账户（创建、编辑、禁用/启用、删除、分配部门、设置角色）。
*   **FR-UM-006**: 支持通过CSV批量导入/更新员工信息。
*   **FR-UM-007**: 员工可修改个人基本信息（如密码）。

#### 3.5.4 角色与权限管理

*   **FR-UM-008**: 系统需内置标准角色：企业管理员、部门经理、普通员工。
*   **FR-UM-009**: 系统需实现基于角色的访问控制（RBAC），不同角色拥有不同的功能和数据访问权限。
    *   企业管理员：拥有所有管理权限和数据访问权限。
    *   部门经理：可查看本部门数据和管理本部门员工信息（可选）。
    *   普通员工：仅可查看个人数据。
*   **FR-UM-010**: 权限控制需覆盖API层面和UI层面。

### 3.6 系统设置与配置模块

#### 3.6.1 企业设置

*   **FR-SC-001**: 企业管理员可配置企业基本信息。
*   **FR-SC-002**: 企业管理员可配置碳排放计算偏好，如：
    *   是否在机票计算中包含辐射强迫指数(RFI)。
    *   默认货币单位。
*   **FR-SC-003**: 企业管理员可配置报表生成频率和通知设置。

#### 3.6.2 集成配置

*   **FR-SC-004**: 企业管理员可配置和管理与外部系统的集成，如同程/携程商旅API、第三方碳排放计算API。
*   **FR-SC-005**: 集成配置界面需提供连接测试功能。

## 4. 非功能需求 (Non-Functional Requirements)

*   **NFR-001 (性能)**: 仪表盘和报表加载时间应在5秒以内（95th percentile）。API平均响应时间应在500ms以内。
*   **NFR-002 (可扩展性)**: 系统需采用微服务架构，支持水平扩展，能够支撑至少1000家企业客户和百万级订单数据量。
*   **NFR-003 (可用性)**: 系统核心功能可用性需达到99.9%。
*   **NFR-004 (安全性)**: 遵循OWASP Top 10安全实践，对敏感数据进行加密存储和传输，实现严格的认证和授权控制，定期进行安全审计和渗透测试。
*   **NFR-005 (易用性)**: UI设计需简洁直观，符合用户习惯。关键操作需有引导和提示。
*   **NFR-006 (可维护性)**: 代码需遵循良好的编程规范，有清晰的文档和注释。采用模块化设计，便于功能迭代和维护。
*   **NFR-007 (数据准确性)**: 碳排放计算结果需尽可能准确，优先使用权威API和标准因子，明确标注计算方法和不确定性。
*   **NFR-008 (合规性)**: 系统设计需考虑GDPR、中国网络安全法等数据隐私法规要求。

## 5. 技术设计

### 5.1 系统架构

采用基于云的微服务架构，主要服务包括：

*   **Web前端 (React)**: 用户交互界面。
*   **API网关**: 统一入口，负责路由、认证、限流。
*   **认证服务**: 处理用户登录、JWT令牌生成与验证。
*   **企业与用户管理服务**: 管理企业、部门、用户信息。
*   **订单与数据同步服务**: 负责与外部商旅平台API对接，同步和存储订单数据。
*   **碳计算服务**: 核心计算引擎，调用内部因子库或外部API计算排放量。
*   **报表与分析服务**: 负责数据聚合、生成报表和仪表盘数据。
*   **AI服务 (独立微服务)**: 包含异常检测、推荐引擎等AI模型和逻辑。
*   **通知服务**: 处理邮件、站内信等通知。
*   **任务调度服务 (Celery)**: 处理异步任务，如数据同步、批量计算、报表生成、模型训练等。

**(详细架构图可参考 `technical_architecture.md` 或 `api_integration_specifications.md` 中的部署图)**

### 5.2 技术栈

*   **后端**: Python 3.11+, Flask/FastAPI
*   **前端**: React 18+, Redux Toolkit, Ant Design, ECharts, Axios
*   **数据库**: PostgreSQL + TimescaleDB (用于存储时间序列的排放数据和订单数据), Redis (缓存、任务队列)
*   **AI/ML**: Scikit-learn, Pandas, NumPy, Statsmodels, (可选) Prophet, Celery
*   **消息队列**: Redis/RabbitMQ (Celery Broker)
*   **部署**: Docker, Kubernetes (可选), 云平台 (阿里云/腾讯云/AWS)
*   **监控**: Prometheus, Grafana, ELK Stack (Elasticsearch, Logstash, Kibana)

### 5.3 数据库设计

详细数据库表结构、关系和字段定义，请参考独立文档：`/home/<USER>/carbon_manager_prd/database_design.md`

### 5.4 碳排放计算模型

详细的计算公式、排放因子来源、API调用逻辑和算法选择，请参考独立文档：`/home/<USER>/carbon_manager_prd/carbon_calculation_models.md`

### 5.5 AI能力设计与集成

AI能力的具体模型选择、输入输出、触发机制、系统集成点和技术实现考虑，请参考独立文档：`/home/<USER>/carbon_manager_prd/ai_integration_design.md`

### 5.6 API接口规范

详细的RESTful API端点定义、请求/响应格式、认证授权、错误处理、版本控制和前后端集成规范，请参考独立文档：`/home/<USER>/carbon_manager_prd/api_integration_specifications.md`

## 6. 实现细节与步骤 (部分示例)

### 6.1 用户故事：企业管理员查看碳排放仪表盘

*   **前端 (React)**:
    1.  创建`DashboardPage`组件。
    2.  在页面加载时 (`useEffect`)，从Redux store获取当前选择的时间范围。
    3.  调用`fetchCarbonEmissionsSummary`异步Action (Redux Toolkit) 发起API请求 (`GET /api/v1/carbon-emissions/summary`)。
    4.  在Action中处理API响应，更新Redux store中的`summary`数据、`loading`和`error`状态。
    5.  `DashboardPage`组件根据`loading`状态显示加载指示器。
    6.  若请求成功，从Redux store读取`summary`数据。
    7.  使用Ant Design的`Card`, `Row`, `Col`, `Statistic`等组件展示KPI指标。
    8.  使用ECharts渲染碳排放趋势图和类型分布图，数据源为`summary.emission_by_period`和`summary.emission_by_type`。
    9.  添加时间范围选择器 (Ant Design `DatePicker.RangePicker`)，选择变化时更新Redux store并重新触发API请求。
    10. 处理API错误，显示Ant Design `Alert`组件。
*   **后端 (Python/Flask/FastAPI)**:
    1.  定义API路由 `GET /api/v1/carbon-emissions/summary`。
    2.  使用装饰器进行JWT认证和权限检查（确保是企业管理员或有权限的角色）。
    3.  从请求查询参数获取`start_date`, `end_date`, `period`, `department_id`等。
    4.  验证查询参数的有效性。
    5.  根据用户JWT中的企业ID和查询参数，调用数据访问层函数。
    6.  数据访问层函数：
        *   查询`order_carbon_emissions`表，按指定时间范围、企业ID、（可选）部门ID聚合总排放量。
        *   按商旅类型聚合排放量。
        *   按指定时间粒度（日/周/月）聚合排放量，用于趋势图。
        *   查询上一周期/去年同期数据进行对比计算。
        *   查询`carbon_emission_anomalies`表获取未处理异常数量。
        *   查询`departments`表获取Top N排放部门。
    7.  使用TimescaleDB的时间序列函数优化聚合查询性能。
    8.  组装API响应数据，遵循标准响应格式。
    9.  实现缓存逻辑（如使用Redis缓存查询结果）。
    10. 处理数据库查询错误和异常。

### 6.2 权限逻辑

*   **API层面**: 使用装饰器或中间件，在每个需要权限控制的API端点上检查JWT中的用户角色和企业ID。
    *   例如，`GET /api/v1/users` 接口只允许企业管理员访问。
    *   `GET /api/v1/carbon-emissions/summary` 接口，企业管理员可以查看整个企业数据，部门经理只能查看其部门数据（通过 department_id 过滤）。
*   **数据层面**: 在数据库查询时，始终加入`enterprise_id`作为过滤条件，确保租户隔离。对于部门经理，还需加入`department_id`过滤。
*   **前端层面**: 根据从后端获取的用户角色信息，动态显示或隐藏UI元素（如管理菜单、操作按钮）。不在前端做最终权限判断，仅作UI展示控制。

### 6.3 异常处理流程

*   **API请求错误**: 前端Axios拦截器统一处理4xx/5xx错误。401跳转登录，其他错误显示友好提示信息（Ant Design `message`或`Alert`）。
*   **后端业务异常**: 后端服务中捕获特定业务异常（如数据验证失败、计算错误），返回包含详细错误信息的标准错误响应。
*   **数据同步失败**: 记录失败原因和相关订单ID，提供重试机制，并在管理界面展示失败列表供管理员处理。
*   **AI模型错误**: AI服务中的模型加载、预测或计算失败时，记录详细错误日志，并向调用方返回特定错误码或回退到默认逻辑（如推荐失败时不显示推荐）。
*   **第三方API错误**: 调用外部API（商旅平台、碳计算API）时，处理网络超时、认证失败、限流等错误，实现重试和熔断机制，并在失败时有备用方案（如使用内置因子计算）。

## 7. 发布标准 (Release Criteria)

*   所有核心功能（数据同步、碳计算、仪表盘、用户管理）按PRD要求实现并通过测试。
*   关键性能指标（API响应时间、页面加载时间）达标。
*   主要用户场景（管理员查看报表、员工查看足迹）测试通过。
*   无P0、P1级别Bug。
*   完成安全测试，无高风险漏洞。
*   部署文档和用户手册准备就绪。

## 8. 未来考虑 (Future Considerations)

*   移动端App开发。
*   与企业内部审批流、费控系统集成。
*   碳抵消功能对接。
*   更高级的AI应用（如供应链碳排放分析）。
*   支持更多类型的数据源导入（如能源消耗数据）。
*   国际化支持。

## 9. 附录

### 9.1 术语表

*   **ESG**: 环境(Environmental)、社会(Social)和公司治理(Governance)。
*   **GHG Protocol**: 温室气体核算体系，国际上最广泛使用的温室气体核算标准。
*   **CO2e**: 二氧化碳当量，衡量温室气体排放的通用单位。
*   **RFI**: 辐射强迫指数，用于评估航空排放对气候变化的额外影响。
*   **TMC**: 差旅管理公司 (Travel Management Company)。
*   **SaaS**: 软件即服务 (Software as a Service)。
*   **RBAC**: 基于角色的访问控制 (Role-Based Access Control)。
*   **JWT**: JSON Web Token。
*   **API**: 应用程序编程接口 (Application Programming Interface)。

### 9.2 参考资料

*   GHG Protocol Corporate Standard: [https://ghgprotocol.org/](https://ghgprotocol.org/)
*   DEFRA Emission Factors: [https://www.gov.uk/government/collections/government-conversion-factors-for-company-reporting](https://www.gov.uk/government/collections/government-conversion-factors-for-company-reporting)
*   ClimateTrade API: [https://climatetrade.com/zh/api-zh/](https://climatetrade.com/zh/api-zh/)
*   同程商旅开放平台 (示例): [http://union.ly.com/plat/unionmodel/api](http://union.ly.com/plat/unionmodel/api)
*   TimescaleDB Documentation: [https://docs.timescale.com/](https://docs.timescale.com/)
*   (内部文档) /home/<USER>/carbon_manager_prd/database_design.md
*   (内部文档) /home/<USER>/carbon_manager_prd/carbon_calculation_models.md
*   (内部文档) /home/<USER>/carbon_manager_prd/ai_integration_design.md
*   (内部文档) /home/<USER>/carbon_manager_prd/api_integration_specifications.md

---
